#!/usr/bin/env python3
"""
Script para automatizar la descarga de reportes de movimientos afiliatorios del IMSS
Autor: Augment Agent
Fecha: 2025-09-03
"""

import os
import time
import logging
from datetime import datetime
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from config import Config
from utils import setup_logging, create_downloads_folder


class IMSSReportDownloader:
    """Clase principal para automatizar la descarga de reportes del IMSS"""
    
    def __init__(self, config: Config):
        self.config = config
        self.driver = None
        self.wait = None
        self.downloads_folder = None
        
    def setup_driver(self):
        """Configura el driver de Chrome con las opciones necesarias"""
        chrome_options = Options()
        
        # Configurar carpeta de descargas
        self.downloads_folder = create_downloads_folder()
        prefs = {
            "download.default_directory": str(self.downloads_folder),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # Opciones adicionales
        if self.config.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Inicializar driver
        service = Service()  # Usa el chromedriver del PATH
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.wait = WebDriverWait(self.driver, self.config.timeout)
        
        logging.info("Driver de Chrome configurado correctamente")
        
    def navigate_to_login(self):
        """Navega a la página de login del IMSS"""
        logging.info("Navegando a la página de login del IMSS...")
        self.driver.get(self.config.base_url)
        
        # Cerrar modal si aparece
        self.close_modal_if_present()
        
    def close_modal_if_present(self):
        """Cierra el modal de información si está presente"""
        try:
            # Estrategia 1: Buscar botón de cerrar con X
            modal_close_button = None
            try:
                modal_close_button = self.wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "button.close[data-dismiss='modal']"))
                )
                modal_close_button.click()
                logging.info("Modal cerrado con botón X")
                time.sleep(2)
                return
            except TimeoutException:
                logging.info("No se encontró botón X del modal")

            # Estrategia 2: Buscar botón "Cerrar"
            try:
                close_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Cerrar')]"))
                )
                close_button.click()
                logging.info("Modal cerrado con botón 'Cerrar'")
                time.sleep(2)
                return
            except TimeoutException:
                logging.info("No se encontró botón 'Cerrar' del modal")

            # Estrategia 3: Presionar ESC
            try:
                self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
                logging.info("Modal cerrado con tecla ESC")
                time.sleep(2)
                return
            except Exception:
                logging.info("No se pudo cerrar modal con ESC")

            # Estrategia 4: JavaScript para cerrar modal
            try:
                self.driver.execute_script("""
                    var modals = document.querySelectorAll('.modal');
                    for(var i = 0; i < modals.length; i++) {
                        modals[i].style.display = 'none';
                    }
                    var backdrops = document.querySelectorAll('.modal-backdrop');
                    for(var i = 0; i < backdrops.length; i++) {
                        backdrops[i].remove();
                    }
                    document.body.classList.remove('modal-open');
                """)
                logging.info("Modal cerrado con JavaScript")
                time.sleep(2)
                return
            except Exception as e:
                logging.warning(f"Error cerrando modal con JavaScript: {e}")

        except Exception as e:
            logging.warning(f"Error general cerrando modal: {e}")

        # Verificar si aún hay modal abierto
        try:
            modal_present = self.driver.find_elements(By.CSS_SELECTOR, ".modal.show, .modal.in, .modal[style*='display: block']")
            if modal_present:
                logging.warning("Aún hay modal presente después de intentar cerrarlo")
            else:
                logging.info("No hay modal presente")
        except:
            pass
            
    def login(self):
        """Realiza el proceso de login con certificado y credenciales"""
        logging.info("Iniciando proceso de login...")

        try:
            # Cerrar cualquier modal que pueda estar abierto antes del login
            self.close_modal_if_present()

            # Subir certificado
            cert_input = self.wait.until(
                EC.presence_of_element_located((By.ID, "certificado"))
            )
            cert_input.send_keys(self.config.certificate_path)
            logging.info("Certificado cargado")

            # Subir llave privada
            key_input = self.wait.until(
                EC.element_to_be_clickable((By.ID, "llave"))
            )
            key_input.send_keys(self.config.private_key_path)
            logging.info("Llave privada cargada")

            # Ingresar usuario
            user_input = self.wait.until(
                EC.presence_of_element_located((By.ID, "idUsuario"))
            )
            user_input.clear()
            user_input.send_keys(self.config.username)
            logging.info("Usuario ingresado")

            # Ingresar contraseña
            password_input = self.wait.until(
                EC.presence_of_element_located((By.ID, "password"))
            )
            password_input.clear()
            password_input.send_keys(self.config.password)
            logging.info("Contraseña ingresada")

            # Cerrar modal nuevamente antes de hacer clic en login
            self.close_modal_if_present()

            # Verificar si ya estamos logueados (por si el modal se cerró automáticamente)
            current_url = self.driver.current_url
            if "AccesoIDSE.idse" in current_url or "Movimientos" in self.driver.page_source:
                logging.info("Ya estamos logueados - el login se completó automáticamente")
                logging.info("Login exitoso")
                return
            else:
                # Hacer clic en "Iniciar sesión" con múltiples estrategias
                # Estrategia 1: Clic normal
                try:
                    login_button = self.wait.until(
                        EC.element_to_be_clickable((By.ID, "botonFirma"))
                    )
                    # Hacer scroll al botón para asegurar que sea visible
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", login_button)
                    time.sleep(1)

                    login_button.click()
                    logging.info("Botón de login presionado (clic normal)")

                    # Esperar un poco y verificar si el login fue exitoso
                    time.sleep(3)
                    current_url = self.driver.current_url
                    if "AccesoIDSE.idse" in current_url:
                        logging.info("Login exitoso después del clic normal")
                        return

                except Exception as e:
                    logging.warning(f"Error con clic normal: {e}")

                    # Verificar si el login se completó durante el intento
                    time.sleep(2)
                    current_url = self.driver.current_url
                    if "AccesoIDSE.idse" in current_url:
                        logging.info("Login completado durante el intento de clic")
                        logging.info("Login exitoso")
                        return
                    else:
                        # Estrategia 2: JavaScript click
                        try:
                            login_button = self.driver.find_element(By.ID, "botonFirma")
                            self.driver.execute_script("arguments[0].click();", login_button)
                            logging.info("Botón de login presionado (JavaScript)")

                            # Esperar y verificar si el login fue exitoso
                            time.sleep(3)
                            current_url = self.driver.current_url
                            if "AccesoIDSE.idse" in current_url:
                                logging.info("Login exitoso después del JavaScript click")
                                return

                        except Exception as js_error:
                            logging.warning(f"Error con JavaScript click: {js_error}")

                            # Verificar nuevamente si el login se completó
                            time.sleep(2)
                            current_url = self.driver.current_url
                            if "AccesoIDSE.idse" in current_url:
                                logging.info("Login completado durante el intento de JavaScript")
                                logging.info("Login exitoso")
                                return
                            else:
                                # Estrategia 3: Ejecutar la función directamente
                                try:
                                    self.driver.execute_script("SignNoApplet();")
                                    logging.info("Login ejecutado con función SignNoApplet()")

                                    # Esperar y verificar si el login fue exitoso
                                    time.sleep(3)
                                    current_url = self.driver.current_url
                                    if "AccesoIDSE.idse" in current_url:
                                        logging.info("Login exitoso después de SignNoApplet()")
                                        return

                                    login_successful = True

                                except Exception as func_error:
                                    logging.error(f"Error ejecutando SignNoApplet(): {func_error}")

                                    # Última verificación
                                    time.sleep(2)
                                    current_url = self.driver.current_url
                                    if "AccesoIDSE.idse" in current_url:
                                        logging.info("Login completado a pesar de los errores")
                                        logging.info("Login exitoso")
                                        return
                                    else:
                                        raise Exception("No se pudo hacer clic en el botón de login con ninguna estrategia")

            # Si llegamos aquí, verificar una última vez si el login fue exitoso
            time.sleep(3)
            current_url = self.driver.current_url
            if "AccesoIDSE.idse" in current_url:
                logging.info("Login exitoso (verificación final)")
                return
            else:
                # Buscar mensajes de error
                try:
                    error_messages = self.driver.find_elements(By.CSS_SELECTOR, ".alert-danger, .error, .mensaje-error")
                    if error_messages:
                        for msg in error_messages:
                            if msg.text.strip():
                                logging.error(f"Mensaje de error encontrado: {msg.text}")
                except:
                    pass

                raise Exception("Login falló - no se pudo completar el proceso")

        except TimeoutException as e:
            logging.error(f"Error durante el login: {e}")
            raise
        except Exception as e:
            logging.error(f"Error inesperado durante el login: {e}")
            raise
            
    def navigate_to_movements(self):
        """Navega a la sección de Movimientos Afiliatorios"""
        logging.info("Navegando a Movimientos Afiliatorios...")

        try:
            # Esperar a que la página principal cargue completamente
            self.wait.until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(2)  # Espera adicional para asegurar carga completa

            # Buscar el enlace de Movimientos Afiliatorios con múltiples estrategias
            movements_link = None

            # Estrategia 1: Por texto parcial
            try:
                movements_link = self.wait.until(
                    EC.element_to_be_clickable((By.PARTIAL_LINK_TEXT, "Movimientos Afiliatorios"))
                )
                logging.info("Enlace encontrado por texto parcial")
            except TimeoutException:
                logging.info("No se encontró por texto parcial, probando con JavaScript...")

                # Estrategia 2: Usar JavaScript para encontrar y hacer clic
                try:
                    self.driver.execute_script("javascript:irA(1);")
                    logging.info("Navegación ejecutada con JavaScript")
                    time.sleep(3)  # Esperar a que cargue la nueva página
                    return
                except Exception as js_error:
                    logging.warning(f"Error con JavaScript: {js_error}")

            if movements_link:
                movements_link.click()
                logging.info("Navegación a Movimientos Afiliatorios exitosa")
                time.sleep(3)  # Esperar a que cargue la nueva página
            else:
                raise Exception("No se pudo encontrar el enlace de Movimientos Afiliatorios")

        except TimeoutException as e:
            logging.error(f"Error navegando a movimientos: {e}")
            raise
        except Exception as e:
            logging.error(f"Error inesperado navegando a movimientos: {e}")
            raise

    def access_consultation(self):
        """Accede a la consulta de todos los resultados"""
        logging.info("Accediendo a consulta de resultados...")

        try:
            # Esperar a que la página de movimientos cargue
            time.sleep(3)

            # Múltiples estrategias para encontrar el botón de consulta
            consultation_button = None

            # Estrategia 1: Por texto exacto
            try:
                consultation_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Consulta todos los resultados aquí')]"))
                )
                logging.info("Botón de consulta encontrado por texto")
            except TimeoutException:
                logging.info("No encontrado por texto, probando con onclick...")

                # Estrategia 2: Por atributo onclick
                try:
                    consultation_button = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(@onclick, \"irA('consulta')\")]"))
                    )
                    logging.info("Botón de consulta encontrado por onclick")
                except TimeoutException:
                    logging.info("No encontrado por onclick, probando con JavaScript...")

                    # Estrategia 3: Ejecutar JavaScript directamente
                    try:
                        self.driver.execute_script("javascript:irA('consulta'); uid_call('resultado.movimientos.afiliatorios', 'clickin')")
                        logging.info("Consulta ejecutada con JavaScript")
                        time.sleep(5)  # Esperar más tiempo para que cargue
                        return
                    except Exception as js_error:
                        logging.warning(f"Error con JavaScript: {js_error}")

            if consultation_button:
                consultation_button.click()
                logging.info("Acceso a consulta exitoso")
                time.sleep(5)  # Esperar a que cargue la página de resultados
            else:
                raise Exception("No se pudo encontrar el botón de consulta")

        except TimeoutException as e:
            logging.error(f"Error accediendo a consulta: {e}")
            raise
        except Exception as e:
            logging.error(f"Error inesperado accediendo a consulta: {e}")
            raise
            
    def set_show_all_records(self):
        """Configura para mostrar todos los registros"""
        logging.info("Configurando para mostrar todos los registros...")

        try:
            # Esperar a que la página de consulta cargue completamente
            time.sleep(3)

            # Buscar el select de paginación con múltiples estrategias
            pagination_select = None

            # Estrategia 1: Por nombre
            try:
                pagination_select = self.wait.until(
                    EC.presence_of_element_located((By.NAME, "paginacion"))
                )
                logging.info("Select de paginación encontrado por nombre")
            except TimeoutException:
                logging.info("No encontrado por nombre, probando por clase...")

                # Estrategia 2: Por clase form-control
                try:
                    pagination_select = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "select.form-control[name='paginacion']"))
                    )
                    logging.info("Select de paginación encontrado por CSS selector")
                except TimeoutException:
                    logging.info("No encontrado por CSS, probando con JavaScript...")

                    # Estrategia 3: JavaScript directo
                    try:
                        self.driver.execute_script("javascript:detalle('/imss/AfiliaResultados.idse')")
                        logging.info("Paginación configurada con JavaScript")
                        time.sleep(5)
                        return
                    except Exception as js_error:
                        logging.warning(f"Error con JavaScript: {js_error}")

            if pagination_select:
                # Verificar que el elemento sea visible e interactuable
                self.driver.execute_script("arguments[0].scrollIntoView(true);", pagination_select)
                time.sleep(1)

                select = Select(pagination_select)

                # Verificar opciones disponibles
                options = select.options
                logging.info(f"Opciones de paginación disponibles: {[opt.get_attribute('value') for opt in options]}")

                # Seleccionar "todos" (valor "0")
                select.select_by_value("0")
                logging.info("Configuración para mostrar todos los registros exitosa")

                # Esperar a que se ejecute el onchange y carguen todos los registros
                time.sleep(8)  # Tiempo más largo para cargar todos los registros

                # Verificar que la tabla se haya cargado
                try:
                    table = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "table.table.table-striped"))
                    )
                    rows = table.find_elements(By.TAG_NAME, "tr")
                    logging.info(f"Tabla cargada con {len(rows)} filas")
                except TimeoutException:
                    logging.warning("No se pudo verificar la carga de la tabla")

            else:
                raise Exception("No se pudo encontrar el select de paginación")

        except TimeoutException as e:
            logging.error(f"Error configurando paginación: {e}")
            raise
        except Exception as e:
            logging.error(f"Error inesperado configurando paginación: {e}")
            raise
            
    def download_csv_reports(self):
        """Descarga todos los reportes CSV disponibles"""
        logging.info("Iniciando descarga de reportes CSV...")

        try:
            # Esperar a que la tabla esté completamente cargada
            time.sleep(3)

            # Buscar todos los enlaces CSV con múltiples estrategias
            csv_links = []

            # Estrategia 1: Por CSS selector específico
            try:
                csv_links = self.driver.find_elements(
                    By.CSS_SELECTOR, "a.label.label-success[href*='ArchivoReporteDetallado']"
                )
                logging.info(f"Encontrados {len(csv_links)} enlaces CSV por CSS selector")
            except Exception as e:
                logging.warning(f"Error con CSS selector: {e}")

            # Estrategia 2: Si no se encontraron, buscar por texto "CSV"
            if not csv_links:
                try:
                    csv_links = self.driver.find_elements(
                        By.XPATH, "//a[contains(@class, 'label-success') and text()='CSV']"
                    )
                    logging.info(f"Encontrados {len(csv_links)} enlaces CSV por XPath")
                except Exception as e:
                    logging.warning(f"Error con XPath: {e}")

            # Estrategia 3: Buscar todos los enlaces que contengan "ArchivoReporteDetallado"
            if not csv_links:
                try:
                    csv_links = self.driver.find_elements(
                        By.XPATH, "//a[contains(@href, 'ArchivoReporteDetallado')]"
                    )
                    # Filtrar solo los que tienen texto "CSV"
                    csv_links = [link for link in csv_links if "CSV" in link.text]
                    logging.info(f"Encontrados {len(csv_links)} enlaces CSV por href")
                except Exception as e:
                    logging.warning(f"Error buscando por href: {e}")

            total_reports = len(csv_links)
            logging.info(f"Total de reportes CSV para descargar: {total_reports}")

            if total_reports == 0:
                logging.warning("No se encontraron reportes CSV para descargar")
                # Intentar tomar screenshot para debug
                try:
                    screenshot_path = f"debug_no_csv_{int(time.time())}.png"
                    self.driver.save_screenshot(screenshot_path)
                    logging.info(f"Screenshot guardado: {screenshot_path}")
                except:
                    pass
                return

            # Descargar cada reporte
            successful_downloads = 0
            for i, link in enumerate(csv_links, 1):
                try:
                    logging.info(f"Descargando reporte {i}/{total_reports}...")

                    # Hacer scroll al elemento para asegurar que sea visible
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", link)
                    time.sleep(1)

                    # Verificar que el enlace sea clickeable
                    self.wait.until(EC.element_to_be_clickable(link))

                    # Hacer clic en el enlace
                    link.click()
                    successful_downloads += 1

                    # Esperar entre descargas para no sobrecargar el servidor
                    time.sleep(3)

                except Exception as e:
                    logging.error(f"Error descargando reporte {i}: {e}")
                    continue

            logging.info(f"Descarga completada. {successful_downloads}/{total_reports} reportes descargados exitosamente")

            # Esperar un poco más para que terminen las descargas
            time.sleep(5)

        except Exception as e:
            logging.error(f"Error durante la descarga de reportes: {e}")
            raise

    def debug_current_page(self, step_name="unknown"):
        """Captura información de debug de la página actual"""
        try:
            current_url = self.driver.current_url
            page_title = self.driver.title

            logging.info(f"=== DEBUG INFO - {step_name} ===")
            logging.info(f"URL actual: {current_url}")
            logging.info(f"Título de página: {page_title}")

            # Tomar screenshot
            screenshot_path = f"debug_{step_name}_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logging.info(f"Screenshot guardado: {screenshot_path}")

            # Buscar elementos comunes para entender el estado de la página
            try:
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                logging.info(f"Botones encontrados: {len(buttons)}")
                for i, btn in enumerate(buttons[:5]):  # Solo los primeros 5
                    btn_text = btn.text.strip()
                    if btn_text:
                        logging.info(f"  Botón {i+1}: '{btn_text}'")
            except:
                pass

            try:
                links = self.driver.find_elements(By.TAG_NAME, "a")
                logging.info(f"Enlaces encontrados: {len(links)}")
                for i, link in enumerate(links[:10]):  # Solo los primeros 10
                    link_text = link.text.strip()
                    if link_text and len(link_text) > 3:
                        logging.info(f"  Enlace {i+1}: '{link_text}'")
            except:
                pass

            logging.info("=== FIN DEBUG INFO ===")

        except Exception as e:
            logging.warning(f"Error capturando debug info: {e}")

    def run(self):
        """Ejecuta el proceso completo de descarga"""
        try:
            logging.info("=== Iniciando proceso de descarga de reportes IMSS ===")

            # Paso 1: Configurar driver
            self.setup_driver()

            # Paso 2: Navegar al login
            self.navigate_to_login()
            self.debug_current_page("after_navigate_to_login")

            # Paso 3: Hacer login
            self.login()
            self.debug_current_page("after_login")

            # Paso 4: Navegar a movimientos
            self.navigate_to_movements()
            self.debug_current_page("after_navigate_to_movements")

            # Paso 5: Acceder a consulta
            try:
                self.access_consultation()
                self.debug_current_page("after_access_consultation")
            except Exception as e:
                logging.error(f"Error en access_consultation: {e}")
                self.debug_current_page("error_access_consultation")
                raise

            # Paso 6: Configurar paginación
            try:
                self.set_show_all_records()
                self.debug_current_page("after_set_show_all_records")
            except Exception as e:
                logging.error(f"Error en set_show_all_records: {e}")
                self.debug_current_page("error_set_show_all_records")
                raise

            # Paso 7: Descargar reportes
            try:
                self.download_csv_reports()
                self.debug_current_page("after_download_csv_reports")
            except Exception as e:
                logging.error(f"Error en download_csv_reports: {e}")
                self.debug_current_page("error_download_csv_reports")
                raise

            logging.info("=== Proceso completado exitosamente ===")
            logging.info(f"Archivos descargados en: {self.downloads_folder}")

        except Exception as e:
            logging.error(f"Error durante la ejecución: {e}")
            self.debug_current_page("final_error")
            raise
        finally:
            if self.driver:
                self.driver.quit()
                logging.info("Driver cerrado")


def main():
    """Función principal"""
    # Configurar logging
    setup_logging()
    
    try:
        # Cargar configuración
        config = Config()
        
        # Crear y ejecutar el descargador
        downloader = IMSSReportDownloader(config)
        downloader.run()
        
    except Exception as e:
        logging.error(f"Error en la ejecución principal: {e}")
        return 1
        
    return 0


if __name__ == "__main__":
    exit(main())
