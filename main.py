#!/usr/bin/env python3
"""
Script para automatizar la descarga de reportes de movimientos afiliatorios del IMSS
Autor: Augment Agent
Fecha: 2025-09-03
"""

import os
import time
import logging
from datetime import datetime
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from config import Config
from utils import setup_logging, create_downloads_folder


class IMSSReportDownloader:
    """Clase principal para automatizar la descarga de reportes del IMSS"""
    
    def __init__(self, config: Config):
        self.config = config
        self.driver = None
        self.wait = None
        self.downloads_folder = None
        
    def setup_driver(self):
        """Configura el driver de Chrome con las opciones necesarias"""
        chrome_options = Options()
        
        # Configurar carpeta de descargas
        self.downloads_folder = create_downloads_folder()
        prefs = {
            "download.default_directory": str(self.downloads_folder),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # Opciones adicionales
        if self.config.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Inicializar driver
        service = Service()  # Usa el chromedriver del PATH
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.wait = WebDriverWait(self.driver, self.config.timeout)
        
        logging.info("Driver de Chrome configurado correctamente")
        
    def navigate_to_login(self):
        """Navega a la página de login del IMSS"""
        logging.info("Navegando a la página de login del IMSS...")
        self.driver.get(self.config.base_url)
        
        # Cerrar modal si aparece
        self.close_modal_if_present()
        
    def wait_for_page_load(self, timeout=10):
        """Espera a que la página cargue completamente"""
        try:
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            time.sleep(1)  # Espera adicional para elementos dinámicos
            return True
        except TimeoutException:
            logging.warning("Timeout esperando que la página cargue")
            return False

    def is_modal_present(self):
        """Verifica si hay un modal presente en la página"""
        try:
            # Método 1: Buscar por texto específico del modal del IMSS
            try:
                modal_text_element = self.driver.find_element(
                    By.XPATH,
                    "//*[contains(text(), 'EXCLUSIVO para PATRONES de PLATAFORMA DIGITAL')]"
                )
                if modal_text_element.is_displayed():
                    logging.info("Modal detectado por texto específico del IMSS")
                    return True
            except:
                pass

            # Método 2: Buscar por ID específico #myModal
            try:
                my_modal = self.driver.find_element(By.ID, "myModal")
                if my_modal.is_displayed():
                    logging.info("Modal detectado por ID #myModal")
                    return True
            except:
                pass

            # Método 3: Buscar múltiples tipos de modals
            modal_selectors = [
                ".modal.show",
                ".modal.in",
                ".modal.fade.in",
                ".modal[style*='display: block']",
                ".modal-backdrop"
            ]

            for selector in modal_selectors:
                modals = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if modals:
                    # Verificar si el modal está realmente visible
                    for modal in modals:
                        if modal.is_displayed():
                            logging.info(f"Modal detectado con selector: {selector}")
                            return True

            return False
        except Exception as e:
            logging.warning(f"Error verificando modal: {e}")
            return False

    def close_modal_if_present(self):
        """Cierra el modal de información si está presente y ESPERA a que se cierre completamente"""
        max_attempts = 5  # Más intentos

        for attempt in range(max_attempts):
            logging.info(f"Verificando modal - intento {attempt + 1}/{max_attempts}")

            if not self.is_modal_present():
                logging.info("No hay modal presente")
                return True

            logging.info("Modal detectado, intentando cerrar...")

            # Estrategia 1: Cerrar modal específico #myModal con JavaScript PRIMERO
            try:
                self.driver.execute_script("""
                    // Cerrar modal específico del IMSS
                    var myModal = document.getElementById('myModal');
                    if (myModal) {
                        myModal.style.display = 'none';
                        myModal.classList.remove('show', 'in', 'fade');
                        console.log('Modal myModal cerrado');
                    }

                    // Cerrar todos los otros modals
                    var modals = document.querySelectorAll('.modal');
                    for(var i = 0; i < modals.length; i++) {
                        modals[i].style.display = 'none';
                        modals[i].classList.remove('show', 'in', 'fade');
                    }

                    // Remover todos los backdrops
                    var backdrops = document.querySelectorAll('.modal-backdrop');
                    for(var i = 0; i < backdrops.length; i++) {
                        backdrops[i].remove();
                    }

                    // Limpiar clases del body
                    document.body.classList.remove('modal-open');
                    document.body.style.paddingRight = '';
                    document.body.style.overflow = '';
                """)
                logging.info("JavaScript ejecutado para cerrar modal #myModal")
                time.sleep(3)  # Más tiempo de espera

                # Verificar que se cerró
                if not self.is_modal_present():
                    logging.info("Modal cerrado exitosamente con JavaScript")
                    return True

            except Exception as e:
                logging.warning(f"Error con JavaScript: {e}")

            # Estrategia 2: Buscar botón "Cerrar" específicamente por el texto del modal
            try:
                # Buscar el botón "Cerrar" que está cerca del texto específico
                close_button = WebDriverWait(self.driver, 3).until(
                    EC.element_to_be_clickable((By.XPATH,
                        "//div[contains(text(), 'EXCLUSIVO para PATRONES')]//ancestor::div[@class='modal-content']//button[contains(text(), 'Cerrar')]"
                    ))
                )
                close_button.click()
                logging.info("Clic en botón 'Cerrar' del modal específico del IMSS")
                time.sleep(3)

                # Verificar que se cerró
                if not self.is_modal_present():
                    logging.info("Modal cerrado exitosamente con botón específico del IMSS")
                    return True

            except TimeoutException:
                logging.info("No se encontró botón cerrar específico del modal IMSS")

            # Estrategia 3: Buscar botón "Cerrar" específicamente en #myModal
            try:
                close_button = WebDriverWait(self.driver, 3).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "#myModal button[data-dismiss='modal']"))
                )
                close_button.click()
                logging.info("Clic en botón cerrar de #myModal")
                time.sleep(3)

                # Verificar que se cerró
                if not self.is_modal_present():
                    logging.info("Modal cerrado exitosamente con botón específico")
                    return True

            except TimeoutException:
                logging.info("No se encontró botón cerrar específico de #myModal")

            # Estrategia 4: Buscar cualquier botón "Cerrar"
            try:
                close_button = WebDriverWait(self.driver, 3).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Cerrar')]"))
                )
                close_button.click()
                logging.info("Clic en botón 'Cerrar' genérico")
                time.sleep(3)

                # Verificar que se cerró
                if not self.is_modal_present():
                    logging.info("Modal cerrado exitosamente con botón 'Cerrar'")
                    return True

            except TimeoutException:
                logging.info("No se encontró botón 'Cerrar' genérico")

            # Estrategia 5: Buscar botón X
            try:
                x_button = WebDriverWait(self.driver, 3).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "button.close[data-dismiss='modal']"))
                )
                x_button.click()
                logging.info("Clic en botón X")
                time.sleep(3)

                # Verificar que se cerró
                if not self.is_modal_present():
                    logging.info("Modal cerrado exitosamente con botón X")
                    return True

            except TimeoutException:
                logging.info("No se encontró botón X")

            # Estrategia 6: ESC
            try:
                self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
                logging.info("Presionada tecla ESC")
                time.sleep(3)

                # Verificar que se cerró
                if not self.is_modal_present():
                    logging.info("Modal cerrado exitosamente con ESC")
                    return True

            except Exception:
                logging.info("No se pudo usar ESC")

            # Estrategia 7: Clic fuera del modal
            try:
                self.driver.execute_script("""
                    // Hacer clic en el backdrop para cerrar modal
                    var backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.click();
                    }
                """)
                logging.info("Clic en backdrop del modal")
                time.sleep(3)

                # Verificar que se cerró
                if not self.is_modal_present():
                    logging.info("Modal cerrado exitosamente con clic en backdrop")
                    return True

            except Exception as e:
                logging.warning(f"Error con clic en backdrop: {e}")

        # Si llegamos aquí, no se pudo cerrar el modal
        if self.is_modal_present():
            logging.error("CRÍTICO: No se pudo cerrar el modal después de todos los intentos")
            # Tomar screenshot para debug
            try:
                screenshot_path = f"modal_error_{int(time.time())}.png"
                self.driver.save_screenshot(screenshot_path)
                logging.error(f"Screenshot del modal problemático: {screenshot_path}")
            except:
                pass
            return False
        else:
            logging.info("Modal ya no está presente")
            return True
            
    def is_logged_in(self):
        """Verifica si ya estamos logueados"""
        try:
            current_url = self.driver.current_url
            page_source = self.driver.page_source

            # Verificar por URL
            if "AccesoIDSE.idse" in current_url:
                return True

            # Verificar por contenido de la página
            if "Movimientos Afiliatorios" in page_source and "Salir" in page_source:
                return True

            return False
        except:
            return False

    def login(self):
        """Realiza el proceso de login con certificado y credenciales - PASO A PASO"""
        logging.info("=== INICIANDO PROCESO DE LOGIN ===")

        try:
            # PASO 1: Esperar a que la página cargue completamente
            logging.info("Paso 1: Esperando que la página cargue...")
            if not self.wait_for_page_load():
                raise Exception("La página no cargó correctamente")
            logging.info("✓ Página cargada")

            # PASO 2: Cerrar modal si está presente y ESPERAR a que se cierre
            logging.info("Paso 2: Verificando y cerrando modal...")
            if not self.close_modal_if_present():
                raise Exception("No se pudo cerrar el modal")
            logging.info("✓ Modal cerrado o no presente")

            # PASO 3: Verificar si ya estamos logueados
            logging.info("Paso 3: Verificando si ya estamos logueados...")
            if self.is_logged_in():
                logging.info("✓ Ya estamos logueados")
                return
            logging.info("✓ No estamos logueados, continuando...")

            # PASO 4: Cargar certificado
            logging.info("Paso 4: Cargando certificado...")
            cert_input = self.wait.until(
                EC.presence_of_element_located((By.ID, "certificado"))
            )
            cert_input.send_keys(self.config.certificate_path)
            logging.info("✓ Certificado cargado")

            # PASO 5: Cargar llave privada
            logging.info("Paso 5: Cargando llave privada...")
            key_input = self.wait.until(
                EC.element_to_be_clickable((By.ID, "llave"))
            )
            key_input.send_keys(self.config.private_key_path)
            logging.info("✓ Llave privada cargada")

            # PASO 6: Ingresar usuario
            logging.info("Paso 6: Ingresando usuario...")
            user_input = self.wait.until(
                EC.presence_of_element_located((By.ID, "idUsuario"))
            )
            user_input.clear()
            user_input.send_keys(self.config.username)
            logging.info("✓ Usuario ingresado")

            # PASO 7: Ingresar contraseña
            logging.info("Paso 7: Ingresando contraseña...")
            password_input = self.wait.until(
                EC.presence_of_element_located((By.ID, "password"))
            )
            password_input.clear()
            password_input.send_keys(self.config.password)
            logging.info("✓ Contraseña ingresada")

            # PASO 8: Verificar nuevamente que no hay modal antes del login
            logging.info("Paso 8: Verificación final de modal antes del login...")
            if self.is_modal_present():
                logging.warning("Modal detectado antes del login, cerrando...")
                if not self.close_modal_if_present():
                    raise Exception("No se pudo cerrar el modal antes del login")
            logging.info("✓ No hay modal presente")

            # PASO 9: Hacer clic en "Iniciar sesión"
            logging.info("Paso 9: Haciendo clic en 'Iniciar sesión'...")

            # Buscar el botón PRIMERO
            login_button = self.wait.until(
                EC.element_to_be_clickable((By.ID, "botonFirma"))
            )

            # Verificar que el botón es realmente clickeable
            if not login_button.is_enabled():
                raise Exception("El botón de login no está habilitado")

            if not login_button.is_displayed():
                raise Exception("El botón de login no está visible")

            # Hacer scroll para asegurar visibilidad
            self.driver.execute_script("arguments[0].scrollIntoView(true);", login_button)
            time.sleep(3)  # Dar más tiempo para que aparezca el modal si va a aparecer

            # VERIFICACIÓN CRÍTICA: El modal puede aparecer DESPUÉS del scroll
            logging.info("Verificando modal después del scroll...")
            if self.is_modal_present():
                logging.warning("¡Modal apareció después del scroll! Cerrando...")
                if not self.close_modal_if_present():
                    raise Exception("No se pudo cerrar el modal que apareció después del scroll")
                logging.info("✓ Modal cerrado después del scroll")

                # Esperar un poco más después de cerrar el modal
                time.sleep(2)

            # Verificación final antes del clic
            if self.is_modal_present():
                logging.error("CRÍTICO: Modal sigue presente después de intentar cerrarlo")
                # Intentar una vez más con JavaScript agresivo
                self.driver.execute_script("""
                    // Forzar cierre de modal con JavaScript más agresivo
                    var modal = document.getElementById('myModal');
                    if (modal) {
                        modal.remove();
                    }

                    // Remover todos los modals y backdrops
                    var allModals = document.querySelectorAll('.modal, .modal-backdrop');
                    for(var i = 0; i < allModals.length; i++) {
                        allModals[i].remove();
                    }

                    // Limpiar body
                    document.body.classList.remove('modal-open');
                    document.body.style.paddingRight = '';
                    document.body.style.overflow = '';
                """)
                time.sleep(2)

                if self.is_modal_present():
                    raise Exception("Modal persistente - no se puede hacer clic en login")

            # Hacer clic
            login_button.click()
            logging.info("✓ Clic en botón de login ejecutado")

            # PASO 10: Esperar y verificar login exitoso
            logging.info("Paso 10: Esperando confirmación de login...")

            # Esperar hasta 30 segundos para que el login se complete
            login_success = False
            for i in range(30):  # 30 segundos máximo
                time.sleep(1)
                if self.is_logged_in():
                    login_success = True
                    break
                logging.info(f"Esperando login... {i+1}/30 segundos")

            if login_success:
                logging.info("✓ LOGIN EXITOSO")
                return
            else:
                # Intentar con JavaScript como último recurso
                logging.warning("Login normal falló, intentando con JavaScript...")
                try:
                    self.driver.execute_script("SignNoApplet();")
                    time.sleep(5)
                    if self.is_logged_in():
                        logging.info("✓ LOGIN EXITOSO con JavaScript")
                        return
                except:
                    pass

                raise Exception("Login falló - no se pudo completar después de 30 segundos")

        except TimeoutException as e:
            logging.error(f"Timeout durante el login: {e}")
            raise
        except Exception as e:
            logging.error(f"Error durante el login: {e}")
            raise
            
    def navigate_to_movements(self):
        """Navega a la sección de Movimientos Afiliatorios - PASO A PASO"""
        logging.info("=== NAVEGANDO A MOVIMIENTOS AFILIATORIOS ===")

        try:
            # PASO 1: Verificar que estamos logueados
            logging.info("Paso 1: Verificando que estamos logueados...")
            if not self.is_logged_in():
                raise Exception("No estamos logueados, no se puede navegar a movimientos")
            logging.info("✓ Confirmado que estamos logueados")

            # PASO 2: Esperar a que la página cargue completamente
            logging.info("Paso 2: Esperando que la página principal cargue...")
            if not self.wait_for_page_load():
                raise Exception("La página principal no cargó correctamente")
            logging.info("✓ Página principal cargada")

            # PASO 3: Buscar el enlace de Movimientos Afiliatorios
            logging.info("Paso 3: Buscando enlace de Movimientos Afiliatorios...")

            movements_link = None
            try:
                movements_link = self.wait.until(
                    EC.element_to_be_clickable((By.PARTIAL_LINK_TEXT, "Movimientos Afiliatorios"))
                )
                logging.info("✓ Enlace encontrado")
            except TimeoutException:
                logging.warning("No se encontró el enlace por texto, intentando con JavaScript...")
                try:
                    self.driver.execute_script("javascript:irA(1);")
                    logging.info("✓ Navegación ejecutada con JavaScript")

                    # Esperar a que cargue la nueva página
                    time.sleep(5)
                    if not self.wait_for_page_load():
                        raise Exception("La página de movimientos no cargó después del JavaScript")

                    logging.info("✓ Navegación a Movimientos Afiliatorios exitosa")
                    return

                except Exception as js_error:
                    raise Exception(f"No se pudo navegar a movimientos: {js_error}")

            # PASO 4: Hacer clic en el enlace
            if movements_link:
                logging.info("Paso 4: Haciendo clic en el enlace...")
                movements_link.click()
                logging.info("✓ Clic ejecutado")

                # PASO 5: Esperar a que cargue la nueva página
                logging.info("Paso 5: Esperando que cargue la página de movimientos...")
                time.sleep(5)
                if not self.wait_for_page_load():
                    raise Exception("La página de movimientos no cargó correctamente")

                logging.info("✓ NAVEGACIÓN A MOVIMIENTOS AFILIATORIOS EXITOSA")
            else:
                raise Exception("No se pudo encontrar el enlace de Movimientos Afiliatorios")

        except Exception as e:
            logging.error(f"Error navegando a movimientos: {e}")
            raise

    def access_consultation(self):
        """Accede a la consulta de todos los resultados"""
        logging.info("Accediendo a consulta de resultados...")

        try:
            # Esperar a que la página de movimientos cargue
            time.sleep(3)

            # Múltiples estrategias para encontrar el botón de consulta
            consultation_button = None

            # Estrategia 1: Por texto exacto
            try:
                consultation_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Consulta todos los resultados aquí')]"))
                )
                logging.info("Botón de consulta encontrado por texto")
            except TimeoutException:
                logging.info("No encontrado por texto, probando con onclick...")

                # Estrategia 2: Por atributo onclick
                try:
                    consultation_button = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, "//button[contains(@onclick, \"irA('consulta')\")]"))
                    )
                    logging.info("Botón de consulta encontrado por onclick")
                except TimeoutException:
                    logging.info("No encontrado por onclick, probando con JavaScript...")

                    # Estrategia 3: Ejecutar JavaScript directamente
                    try:
                        self.driver.execute_script("javascript:irA('consulta'); uid_call('resultado.movimientos.afiliatorios', 'clickin')")
                        logging.info("Consulta ejecutada con JavaScript")
                        time.sleep(5)  # Esperar más tiempo para que cargue
                        return
                    except Exception as js_error:
                        logging.warning(f"Error con JavaScript: {js_error}")

            if consultation_button:
                consultation_button.click()
                logging.info("Acceso a consulta exitoso")
                time.sleep(5)  # Esperar a que cargue la página de resultados
            else:
                raise Exception("No se pudo encontrar el botón de consulta")

        except TimeoutException as e:
            logging.error(f"Error accediendo a consulta: {e}")
            raise
        except Exception as e:
            logging.error(f"Error inesperado accediendo a consulta: {e}")
            raise
            
    def set_show_all_records(self):
        """Configura para mostrar todos los registros"""
        logging.info("Configurando para mostrar todos los registros...")

        try:
            # Esperar a que la página de consulta cargue completamente
            time.sleep(3)

            # Buscar el select de paginación con múltiples estrategias
            pagination_select = None

            # Estrategia 1: Por nombre
            try:
                pagination_select = self.wait.until(
                    EC.presence_of_element_located((By.NAME, "paginacion"))
                )
                logging.info("Select de paginación encontrado por nombre")
            except TimeoutException:
                logging.info("No encontrado por nombre, probando por clase...")

                # Estrategia 2: Por clase form-control
                try:
                    pagination_select = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "select.form-control[name='paginacion']"))
                    )
                    logging.info("Select de paginación encontrado por CSS selector")
                except TimeoutException:
                    logging.info("No encontrado por CSS, probando con JavaScript...")

                    # Estrategia 3: JavaScript directo
                    try:
                        self.driver.execute_script("javascript:detalle('/imss/AfiliaResultados.idse')")
                        logging.info("Paginación configurada con JavaScript")
                        time.sleep(5)
                        return
                    except Exception as js_error:
                        logging.warning(f"Error con JavaScript: {js_error}")

            if pagination_select:
                # Verificar que el elemento sea visible e interactuable
                self.driver.execute_script("arguments[0].scrollIntoView(true);", pagination_select)
                time.sleep(1)

                select = Select(pagination_select)

                # Verificar opciones disponibles
                options = select.options
                logging.info(f"Opciones de paginación disponibles: {[opt.get_attribute('value') for opt in options]}")

                # Seleccionar "todos" (valor "0")
                select.select_by_value("0")
                logging.info("Configuración para mostrar todos los registros exitosa")

                # Esperar a que se ejecute el onchange y carguen todos los registros
                time.sleep(8)  # Tiempo más largo para cargar todos los registros

                # Verificar que la tabla se haya cargado
                try:
                    table = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "table.table.table-striped"))
                    )
                    rows = table.find_elements(By.TAG_NAME, "tr")
                    logging.info(f"Tabla cargada con {len(rows)} filas")
                except TimeoutException:
                    logging.warning("No se pudo verificar la carga de la tabla")

            else:
                raise Exception("No se pudo encontrar el select de paginación")

        except TimeoutException as e:
            logging.error(f"Error configurando paginación: {e}")
            raise
        except Exception as e:
            logging.error(f"Error inesperado configurando paginación: {e}")
            raise
            
    def download_csv_reports(self):
        """Descarga todos los reportes CSV disponibles"""
        logging.info("Iniciando descarga de reportes CSV...")

        try:
            # Esperar a que la tabla esté completamente cargada
            time.sleep(3)

            # Buscar todos los enlaces CSV con múltiples estrategias
            csv_links = []

            # Estrategia 1: Por CSS selector específico
            try:
                csv_links = self.driver.find_elements(
                    By.CSS_SELECTOR, "a.label.label-success[href*='ArchivoReporteDetallado']"
                )
                logging.info(f"Encontrados {len(csv_links)} enlaces CSV por CSS selector")
            except Exception as e:
                logging.warning(f"Error con CSS selector: {e}")

            # Estrategia 2: Si no se encontraron, buscar por texto "CSV"
            if not csv_links:
                try:
                    csv_links = self.driver.find_elements(
                        By.XPATH, "//a[contains(@class, 'label-success') and text()='CSV']"
                    )
                    logging.info(f"Encontrados {len(csv_links)} enlaces CSV por XPath")
                except Exception as e:
                    logging.warning(f"Error con XPath: {e}")

            # Estrategia 3: Buscar todos los enlaces que contengan "ArchivoReporteDetallado"
            if not csv_links:
                try:
                    csv_links = self.driver.find_elements(
                        By.XPATH, "//a[contains(@href, 'ArchivoReporteDetallado')]"
                    )
                    # Filtrar solo los que tienen texto "CSV"
                    csv_links = [link for link in csv_links if "CSV" in link.text]
                    logging.info(f"Encontrados {len(csv_links)} enlaces CSV por href")
                except Exception as e:
                    logging.warning(f"Error buscando por href: {e}")

            total_reports = len(csv_links)
            logging.info(f"Total de reportes CSV para descargar: {total_reports}")

            if total_reports == 0:
                logging.warning("No se encontraron reportes CSV para descargar")
                # Intentar tomar screenshot para debug
                try:
                    screenshot_path = f"debug_no_csv_{int(time.time())}.png"
                    self.driver.save_screenshot(screenshot_path)
                    logging.info(f"Screenshot guardado: {screenshot_path}")
                except:
                    pass
                return

            # Descargar cada reporte
            successful_downloads = 0
            for i, link in enumerate(csv_links, 1):
                try:
                    logging.info(f"Descargando reporte {i}/{total_reports}...")

                    # Hacer scroll al elemento para asegurar que sea visible
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", link)
                    time.sleep(1)

                    # Verificar que el enlace sea clickeable
                    self.wait.until(EC.element_to_be_clickable(link))

                    # Hacer clic en el enlace
                    link.click()
                    successful_downloads += 1

                    # Esperar entre descargas para no sobrecargar el servidor
                    time.sleep(3)

                except Exception as e:
                    logging.error(f"Error descargando reporte {i}: {e}")
                    continue

            logging.info(f"Descarga completada. {successful_downloads}/{total_reports} reportes descargados exitosamente")

            # Esperar un poco más para que terminen las descargas
            time.sleep(5)

        except Exception as e:
            logging.error(f"Error durante la descarga de reportes: {e}")
            raise

    def debug_current_page(self, step_name="unknown"):
        """Captura información de debug de la página actual"""
        try:
            current_url = self.driver.current_url
            page_title = self.driver.title

            logging.info(f"=== DEBUG INFO - {step_name} ===")
            logging.info(f"URL actual: {current_url}")
            logging.info(f"Título de página: {page_title}")

            # Tomar screenshot
            screenshot_path = f"debug_{step_name}_{int(time.time())}.png"
            self.driver.save_screenshot(screenshot_path)
            logging.info(f"Screenshot guardado: {screenshot_path}")

            # Buscar elementos comunes para entender el estado de la página
            try:
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                logging.info(f"Botones encontrados: {len(buttons)}")
                for i, btn in enumerate(buttons[:5]):  # Solo los primeros 5
                    btn_text = btn.text.strip()
                    if btn_text:
                        logging.info(f"  Botón {i+1}: '{btn_text}'")
            except:
                pass

            try:
                links = self.driver.find_elements(By.TAG_NAME, "a")
                logging.info(f"Enlaces encontrados: {len(links)}")
                for i, link in enumerate(links[:10]):  # Solo los primeros 10
                    link_text = link.text.strip()
                    if link_text and len(link_text) > 3:
                        logging.info(f"  Enlace {i+1}: '{link_text}'")
            except:
                pass

            logging.info("=== FIN DEBUG INFO ===")

        except Exception as e:
            logging.warning(f"Error capturando debug info: {e}")

    def run(self):
        """Ejecuta el proceso completo de descarga"""
        try:
            logging.info("=== Iniciando proceso de descarga de reportes IMSS ===")

            # Paso 1: Configurar driver
            self.setup_driver()

            # Paso 2: Navegar al login
            self.navigate_to_login()
            self.debug_current_page("after_navigate_to_login")

            # Paso 3: Hacer login
            self.login()
            self.debug_current_page("after_login")

            # Paso 4: Navegar a movimientos
            self.navigate_to_movements()
            self.debug_current_page("after_navigate_to_movements")

            # Paso 5: Acceder a consulta
            try:
                self.access_consultation()
                self.debug_current_page("after_access_consultation")
            except Exception as e:
                logging.error(f"Error en access_consultation: {e}")
                self.debug_current_page("error_access_consultation")
                raise

            # Paso 6: Configurar paginación
            try:
                self.set_show_all_records()
                self.debug_current_page("after_set_show_all_records")
            except Exception as e:
                logging.error(f"Error en set_show_all_records: {e}")
                self.debug_current_page("error_set_show_all_records")
                raise

            # Paso 7: Descargar reportes
            try:
                self.download_csv_reports()
                self.debug_current_page("after_download_csv_reports")
            except Exception as e:
                logging.error(f"Error en download_csv_reports: {e}")
                self.debug_current_page("error_download_csv_reports")
                raise

            logging.info("=== Proceso completado exitosamente ===")
            logging.info(f"Archivos descargados en: {self.downloads_folder}")

        except Exception as e:
            logging.error(f"Error durante la ejecución: {e}")
            self.debug_current_page("final_error")
            raise
        finally:
            if self.driver:
                self.driver.quit()
                logging.info("Driver cerrado")


def main():
    """Función principal"""
    # Configurar logging
    setup_logging()
    
    try:
        # Cargar configuración
        config = Config()
        
        # Crear y ejecutar el descargador
        downloader = IMSSReportDownloader(config)
        downloader.run()
        
    except Exception as e:
        logging.error(f"Error en la ejecución principal: {e}")
        return 1
        
    return 0


if __name__ == "__main__":
    exit(main())
